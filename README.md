# 太空射击游戏 (Space Shooter)

一个使用 Rust 和 Bevy 引擎开发的经典太空射击游戏。

## 功能特性

- ✅ 玩家飞船控制（WASD 或方向键移动）
- ✅ 射击系统（空格键发射子弹）
- ✅ 敌人生成和AI移动
- ✅ 碰撞检测系统
- ✅ 计分系统
- ✅ 游戏结束和重启功能
- ✅ 实时清理超出屏幕的实体

## 游戏操作

### 游戏中
- **移动**: `WASD` 键或方向键
- **射击**: `空格键`
- **退出**: 关闭窗口

### 游戏结束后
- **重新开始**: `R` 键

## 运行游戏

确保你已经安装了 Rust 和 Cargo，然后在项目目录中运行：

```bash
cargo run
```

## 系统要求

- Rust 1.70+
- macOS 10.15+ (支持 Metal)
- 至少 4GB RAM

## 技术实现

### 使用的技术栈
- **Rust**: 系统编程语言
- **Bevy 0.12**: 现代游戏引擎
- **ECS架构**: Entity-Component-System 设计模式

### 核心系统
1. **玩家控制系统**: 处理键盘输入和飞船移动
2. **射击系统**: 管理子弹的生成、移动和生命周期
3. **敌人系统**: 随机生成敌人并控制其移动
4. **碰撞检测系统**: 检测子弹与敌人、玩家与敌人的碰撞
5. **清理系统**: 移除超出屏幕边界的实体
6. **游戏状态系统**: 管理游戏进行和结束状态

### 游戏对象
- **玩家飞船**: 蓝色方块，可以移动和射击
- **敌人**: 红色方块，从屏幕顶部向下移动
- **子弹**: 黄色小矩形，向上移动

## 开发说明

游戏使用简单的彩色矩形代替复杂的图像资源，专注于游戏逻辑的实现。这使得游戏能够快速运行，同时展示了 Bevy 引擎的核心功能。

## 可能的扩展

- 添加音效和背景音乐
- 实现不同类型的敌人
- 添加道具系统
- 实现关卡系统
- 添加粒子效果
- 保存最高分记录

## 许可证

MIT License
