# 🚀 太空射击游戏 (Space Shooter)

一个使用 Rust 和 Bevy 引擎开发的经典太空射击游戏，具有精美的太空主题视觉效果。

## 🎮 功能特性

- ✅ **太空飞船控制** - WASD 或方向键移动，流畅的飞行体验
- ✅ **激光射击系统** - 空格键发射绿色激光束
- ✅ **陨石威胁** - 随机生成的陨石从太空深处袭来
- ✅ **精美视觉效果** - 深空背景、太空飞船和陨石的真实图像
- ✅ **碰撞检测系统** - 精确的物理碰撞检测
- ✅ **计分系统** - 击毁陨石获得分数
- ✅ **游戏结束和重启** - 完整的游戏循环
- ✅ **优化的UI界面** - 中文界面，美观的字体和布局
- ✅ **性能优化** - 自动清理超出屏幕的实体

## 游戏操作

### 游戏中
- **移动**: `WASD` 键或方向键
- **射击**: `空格键`
- **退出**: 关闭窗口

### 游戏结束后
- **重新开始**: `R` 键

## 运行游戏

确保你已经安装了 Rust 和 Cargo，然后在项目目录中运行：

```bash
cargo run
```

## 系统要求

- Rust 1.70+
- macOS 10.15+ (支持 Metal)
- 至少 4GB RAM

## 技术实现

### 使用的技术栈
- **Rust**: 系统编程语言
- **Bevy 0.12**: 现代游戏引擎
- **ECS架构**: Entity-Component-System 设计模式

### 核心系统
1. **玩家控制系统**: 处理键盘输入和飞船移动
2. **射击系统**: 管理子弹的生成、移动和生命周期
3. **敌人系统**: 随机生成敌人并控制其移动
4. **碰撞检测系统**: 检测子弹与敌人、玩家与敌人的碰撞
5. **清理系统**: 移除超出屏幕边界的实体
6. **游戏状态系统**: 管理游戏进行和结束状态

### 🎨 游戏对象
- **太空飞船**: 银色战斗机，配备蓝色引擎喷射效果
- **陨石**: 不规则的棕灰色陨石，从太空深处飞来
- **激光束**: 绿色高能激光，用于摧毁陨石
- **太空背景**: 深邃的星空背景，营造真实的太空氛围

## 🛠️ 开发说明

游戏包含了完整的视觉资源系统，包括：
- **程序化生成的图像资源** - 使用 Rust 代码生成太空主题的图像
- **优化的渲染管线** - 利用 Bevy 的高性能 2D 渲染系统
- **模块化的资源管理** - 易于替换和扩展的资源系统

所有图像资源都是通过代码生成的，确保了项目的完整性和可移植性。

## 可能的扩展

- 添加音效和背景音乐
- 实现不同类型的敌人
- 添加道具系统
- 实现关卡系统
- 添加粒子效果
- 保存最高分记录

## 许可证

MIT License
