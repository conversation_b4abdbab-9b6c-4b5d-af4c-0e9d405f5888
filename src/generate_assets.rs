use image::{<PERSON><PERSON><PERSON><PERSON>, <PERSON>g<PERSON>, RgbaImage};
use std::path::Path;

pub fn generate_all_assets() {
    println!("🎨 生成游戏资源...");
    
    // 创建assets目录
    std::fs::create_dir_all("assets").unwrap();
    
    // 生成太空背景
    generate_space_background();
    
    // 生成太空飞船
    generate_spaceship();
    
    // 生成陨石
    generate_asteroid();
    
    // 生成激光子弹
    generate_laser();
    
    println!("✅ 所有资源生成完成！");
}

fn generate_space_background() {
    let width = 800;
    let height = 600;
    let mut img: RgbaImage = ImageBuffer::new(width, height);

    // 创建深空背景（深蓝到黑色渐变）
    for (_x, y, pixel) in img.enumerate_pixels_mut() {
        let gradient = (y as f32 / height as f32) * 0.3;
        let r = (10.0 * gradient) as u8;
        let g = (15.0 * gradient) as u8;
        let b = (40.0 + 60.0 * gradient) as u8;
        *pixel = Rgba([r, g, b, 255]); // 完全不透明
    }
    
    // 添加星星
    use rand::prelude::*;
    let mut rng = thread_rng();
    for _ in 0..200 {
        let x = rng.gen_range(0..width);
        let y = rng.gen_range(0..height);
        let brightness = rng.gen_range(150..255);
        
        if x < width && y < height {
            img.put_pixel(x, y, Rgba([brightness, brightness, brightness, 255]));

            // 有些星星稍微大一点
            if rng.gen_bool(0.3) && x > 0 && y > 0 && x < width - 1 && y < height - 1 {
                img.put_pixel(x + 1, y, Rgba([brightness / 2, brightness / 2, brightness / 2, 255]));
                img.put_pixel(x, y + 1, Rgba([brightness / 2, brightness / 2, brightness / 2, 255]));
            }
        }
    }
    
    img.save("assets/space_background.png").unwrap();
    println!("📸 太空背景已生成");
}

fn generate_spaceship() {
    let width = 80;  // 增大尺寸
    let height = 80;
    let mut img: RgbaImage = ImageBuffer::new(width, height);

    // 透明背景
    for pixel in img.pixels_mut() {
        *pixel = Rgba([0, 0, 0, 0]); // 完全透明
    }

    let center_x = width / 2;
    let _center_y = height / 2;
    
    // 绘制飞船主体（银色三角形）- 更大更详细
    for y in 15..65 {
        let wing_width = ((y - 15) as f32 / 50.0 * 30.0) as u32;
        for x in (center_x - wing_width / 2)..(center_x + wing_width / 2) {
            if x < width && y < height {
                img.put_pixel(x, y, Rgba([180, 180, 200, 255])); // 银色，不透明
            }
        }
    }

    // 飞船顶部（更亮的银色）
    for y in 15..25 {
        let tip_width = ((y - 15) as f32 / 10.0 * 12.0) as u32;
        for x in (center_x - tip_width / 2)..(center_x + tip_width / 2) {
            if x < width && y < height {
                img.put_pixel(x, y, Rgba([220, 220, 240, 255])); // 亮银色
            }
        }
    }

    // 飞船中央线（更亮）
    for y in 20..60 {
        if center_x < width && y < height {
            img.put_pixel(center_x, y, Rgba([240, 240, 255, 255])); // 非常亮的银色
        }
    }

    // 引擎喷口（蓝色）- 更大
    for y in 60..68 {
        for x in (center_x - 5)..(center_x + 5) {
            if x < width && y < height {
                img.put_pixel(x, y, Rgba([50, 150, 255, 255])); // 蓝色引擎
            }
        }
    }

    // 引擎火焰效果
    for y in 68..75 {
        let flame_width = ((75 - y) as f32 / 7.0 * 8.0) as u32;
        for x in (center_x - flame_width / 2)..(center_x + flame_width / 2) {
            if x < width && y < height {
                img.put_pixel(x, y, Rgba([100, 200, 255, 180])); // 半透明蓝色火焰
            }
        }
    }
    
    img.save("assets/player.png").unwrap();
    println!("🚀 太空飞船已生成");
}

fn generate_asteroid() {
    let width = 60;  // 增大尺寸
    let height = 60;
    let mut img: RgbaImage = ImageBuffer::new(width, height);

    // 透明背景
    for pixel in img.pixels_mut() {
        *pixel = Rgba([0, 0, 0, 0]); // 完全透明
    }
    
    let center_x = width as f32 / 2.0;
    let center_y = height as f32 / 2.0;
    let radius = 25.0;  // 增大半径
    
    use rand::prelude::*;
    let mut rng = thread_rng();
    
    // 绘制不规则的陨石
    for y in 0..height {
        for x in 0..width {
            let dx = x as f32 - center_x;
            let dy = y as f32 - center_y;
            let distance = (dx * dx + dy * dy).sqrt();
            
            // 添加一些随机性使陨石看起来不规则
            let noise = rng.gen_range(-3.0..3.0);
            let adjusted_radius = radius + noise;
            
            if distance < adjusted_radius {
                // 陨石颜色（棕灰色系）
                let base_color = 80 + (distance / radius * 40.0) as u8;
                let r = base_color + rng.gen_range(0..20);
                let g = base_color - rng.gen_range(0..15);
                let b = base_color - rng.gen_range(0..25);

                img.put_pixel(x, y, Rgba([r, g, b, 255])); // 不透明
            }
        }
    }
    
    img.save("assets/enemy.png").unwrap();
    println!("☄️ 陨石已生成");
}

fn generate_laser() {
    let width = 12;  // 增大宽度
    let height = 24; // 增大高度
    let mut img: RgbaImage = ImageBuffer::new(width, height);

    // 透明背景
    for pixel in img.pixels_mut() {
        *pixel = Rgba([0, 0, 0, 0]); // 完全透明
    }

    // 绘制激光束（亮绿色）
    for y in 0..height {
        for x in 3..9 {
            let intensity = 255 - (((x as f32 - 6.0).abs() / 3.0) * 100.0) as u8;
            img.put_pixel(x, y, Rgba([0, intensity, 0, 255])); // 绿色激光，不透明
        }
    }

    // 激光核心（更亮更宽）
    for y in 0..height {
        img.put_pixel(5, y, Rgba([150, 255, 150, 255]));
        img.put_pixel(6, y, Rgba([150, 255, 150, 255]));
        img.put_pixel(7, y, Rgba([150, 255, 150, 255]));
    }
    
    img.save("assets/bullet.png").unwrap();
    println!("⚡ 激光子弹已生成");
}

fn main() {
    generate_all_assets();
}
