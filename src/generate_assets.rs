use image::{<PERSON>Buffer, <PERSON>g<PERSON>, RgbaImage};

pub fn generate_all_assets() {
    println!("🎨 生成游戏资源...");
    
    // 创建assets目录
    std::fs::create_dir_all("assets").unwrap();
    
    // 生成太空背景
    generate_space_background();
    
    // 生成太空飞船
    generate_spaceship();
    
    // 生成陨石
    generate_asteroid();
    
    // 生成激光子弹
    generate_laser();
    
    println!("✅ 所有资源生成完成！");
}

fn generate_space_background() {
    let width = 800;
    let height = 600;
    let mut img: RgbaImage = ImageBuffer::new(width, height);

    // 创建深空背景（深蓝到黑色渐变）
    for (_x, y, pixel) in img.enumerate_pixels_mut() {
        let gradient = (y as f32 / height as f32) * 0.3;
        let r = (10.0 * gradient) as u8;
        let g = (15.0 * gradient) as u8;
        let b = (40.0 + 60.0 * gradient) as u8;
        *pixel = Rgba([r, g, b, 255]); // 完全不透明
    }
    
    // 添加星星
    use rand::prelude::*;
    let mut rng = thread_rng();
    for _ in 0..200 {
        let x = rng.gen_range(0..width);
        let y = rng.gen_range(0..height);
        let brightness = rng.gen_range(150..255);
        
        if x < width && y < height {
            img.put_pixel(x, y, Rgba([brightness, brightness, brightness, 255]));

            // 有些星星稍微大一点
            if rng.gen_bool(0.3) && x > 0 && y > 0 && x < width - 1 && y < height - 1 {
                img.put_pixel(x + 1, y, Rgba([brightness / 2, brightness / 2, brightness / 2, 255]));
                img.put_pixel(x, y + 1, Rgba([brightness / 2, brightness / 2, brightness / 2, 255]));
            }
        }
    }
    
    img.save("assets/space_background.png").unwrap();
    println!("📸 太空背景已生成");
}

fn generate_spaceship() {
    let width = 90;  // 增大尺寸适应J-20设计
    let height = 90;
    let mut img: RgbaImage = ImageBuffer::new(width, height);

    // 透明背景
    for pixel in img.pixels_mut() {
        *pixel = Rgba([0, 0, 0, 0]); // 完全透明
    }

    let center_x = width / 2;

    // J-20主机身（深灰色隐形涂装）
    for y in 20..75 {
        let body_width = if y < 35 {
            // 机头部分，逐渐变宽
            ((y - 20) as f32 / 15.0 * 16.0) as u32
        } else if y < 60 {
            // 主机身部分
            16 + ((y - 35) as f32 / 25.0 * 8.0) as u32
        } else {
            // 机尾部分，逐渐变窄
            24 - ((y - 60) as f32 / 15.0 * 8.0) as u32
        };

        for x in (center_x - body_width / 2)..(center_x + body_width / 2) {
            if x < width && y < height {
                img.put_pixel(x, y, Rgba([60, 65, 70, 255])); // 深灰色机身
            }
        }
    }

    // J-20鸭翼（前翼）
    for y in 25..35 {
        for x in 15..25 {
            if x < width && y < height {
                img.put_pixel(x, y, Rgba([70, 75, 80, 255])); // 左鸭翼
            }
        }
        for x in 65..75 {
            if x < width && y < height {
                img.put_pixel(x, y, Rgba([70, 75, 80, 255])); // 右鸭翼
            }
        }
    }

    // 主翼（三角翼设计）
    for y in 40..65 {
        let wing_span = ((y - 40) as f32 / 25.0 * 35.0) as u32;
        // 左翼
        for x in (center_x - 12 - wing_span)..(center_x - 12) {
            if x < width && y < height {
                img.put_pixel(x, y, Rgba([65, 70, 75, 255]));
            }
        }
        // 右翼
        for x in (center_x + 12)..(center_x + 12 + wing_span) {
            if x < width && y < height {
                img.put_pixel(x, y, Rgba([65, 70, 75, 255]));
            }
        }
    }

    // 驾驶舱（深色玻璃效果）
    for y in 25..40 {
        let cockpit_width = ((y - 25) as f32 / 15.0 * 8.0) as u32;
        for x in (center_x - cockpit_width / 2)..(center_x + cockpit_width / 2) {
            if x < width && y < height {
                img.put_pixel(x, y, Rgba([20, 30, 40, 255])); // 深蓝色驾驶舱
            }
        }
    }

    // 机身细节线条
    for y in 30..70 {
        if center_x < width && y < height {
            img.put_pixel(center_x, y, Rgba([80, 85, 90, 255])); // 中央线
        }
        if center_x - 6 < width && y < height {
            img.put_pixel(center_x - 6, y, Rgba([75, 80, 85, 255])); // 左侧线
        }
        if center_x + 6 < width && y < height {
            img.put_pixel(center_x + 6, y, Rgba([75, 80, 85, 255])); // 右侧线
        }
    }

    // 双发动机喷口（J-20特色）
    for y in 70..78 {
        // 左发动机
        for x in (center_x - 8)..(center_x - 3) {
            if x < width && y < height {
                img.put_pixel(x, y, Rgba([255, 100, 50, 255])); // 橙红色喷口
            }
        }
        // 右发动机
        for x in (center_x + 3)..(center_x + 8) {
            if x < width && y < height {
                img.put_pixel(x, y, Rgba([255, 100, 50, 255])); // 橙红色喷口
            }
        }
    }

    // 引擎火焰效果
    for y in 78..85 {
        let flame_intensity = ((85 - y) as f32 / 7.0 * 255.0) as u8;
        // 左引擎火焰
        for x in (center_x - 7)..(center_x - 2) {
            if x < width && y < height {
                img.put_pixel(x, y, Rgba([255, flame_intensity, 50, 180]));
            }
        }
        // 右引擎火焰
        for x in (center_x + 2)..(center_x + 7) {
            if x < width && y < height {
                img.put_pixel(x, y, Rgba([255, flame_intensity, 50, 180]));
            }
        }
    }
    
    img.save("assets/player.png").unwrap();
    println!("🚀 太空飞船已生成");
}

fn generate_asteroid() {
    let width = 60;  // 增大尺寸
    let height = 60;
    let mut img: RgbaImage = ImageBuffer::new(width, height);

    // 透明背景
    for pixel in img.pixels_mut() {
        *pixel = Rgba([0, 0, 0, 0]); // 完全透明
    }
    
    let center_x = width as f32 / 2.0;
    let center_y = height as f32 / 2.0;
    let radius = 25.0;  // 增大半径
    
    use rand::prelude::*;
    let mut rng = thread_rng();
    
    // 绘制不规则的陨石
    for y in 0..height {
        for x in 0..width {
            let dx = x as f32 - center_x;
            let dy = y as f32 - center_y;
            let distance = (dx * dx + dy * dy).sqrt();
            
            // 添加一些随机性使陨石看起来不规则
            let noise = rng.gen_range(-3.0..3.0);
            let adjusted_radius = radius + noise;
            
            if distance < adjusted_radius {
                // 陨石颜色（棕灰色系）
                let base_color = 80 + (distance / radius * 40.0) as u8;
                let r = base_color + rng.gen_range(0..20);
                let g = base_color - rng.gen_range(0..15);
                let b = base_color - rng.gen_range(0..25);

                img.put_pixel(x, y, Rgba([r, g, b, 255])); // 不透明
            }
        }
    }
    
    img.save("assets/enemy.png").unwrap();
    println!("☄️ 陨石已生成");
}

fn generate_laser() {
    let width = 12;  // 增大宽度
    let height = 24; // 增大高度
    let mut img: RgbaImage = ImageBuffer::new(width, height);

    // 透明背景
    for pixel in img.pixels_mut() {
        *pixel = Rgba([0, 0, 0, 0]); // 完全透明
    }

    // 绘制激光束（亮绿色）
    for y in 0..height {
        for x in 3..9 {
            let intensity = 255 - (((x as f32 - 6.0).abs() / 3.0) * 100.0) as u8;
            img.put_pixel(x, y, Rgba([0, intensity, 0, 255])); // 绿色激光，不透明
        }
    }

    // 激光核心（更亮更宽）
    for y in 0..height {
        img.put_pixel(5, y, Rgba([150, 255, 150, 255]));
        img.put_pixel(6, y, Rgba([150, 255, 150, 255]));
        img.put_pixel(7, y, Rgba([150, 255, 150, 255]));
    }
    
    img.save("assets/bullet.png").unwrap();
    println!("⚡ 激光子弹已生成");
}

fn main() {
    generate_all_assets();
}
