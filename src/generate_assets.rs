use image::{<PERSON>Buffer, Rgb, RgbImage};
use std::path::Path;

pub fn generate_all_assets() {
    println!("🎨 生成游戏资源...");
    
    // 创建assets目录
    std::fs::create_dir_all("assets").unwrap();
    
    // 生成太空背景
    generate_space_background();
    
    // 生成太空飞船
    generate_spaceship();
    
    // 生成陨石
    generate_asteroid();
    
    // 生成激光子弹
    generate_laser();
    
    println!("✅ 所有资源生成完成！");
}

fn generate_space_background() {
    let width = 800;
    let height = 600;
    let mut img: RgbImage = ImageBuffer::new(width, height);
    
    // 创建深空背景（深蓝到黑色渐变）
    for (x, y, pixel) in img.enumerate_pixels_mut() {
        let gradient = (y as f32 / height as f32) * 0.3;
        let r = (10.0 * gradient) as u8;
        let g = (15.0 * gradient) as u8;
        let b = (40.0 + 60.0 * gradient) as u8;
        *pixel = Rgb([r, g, b]);
    }
    
    // 添加星星
    use rand::prelude::*;
    let mut rng = thread_rng();
    for _ in 0..200 {
        let x = rng.gen_range(0..width);
        let y = rng.gen_range(0..height);
        let brightness = rng.gen_range(150..255);
        
        if x < width && y < height {
            img.put_pixel(x, y, Rgb([brightness, brightness, brightness]));
            
            // 有些星星稍微大一点
            if rng.gen_bool(0.3) && x > 0 && y > 0 && x < width - 1 && y < height - 1 {
                img.put_pixel(x + 1, y, Rgb([brightness / 2, brightness / 2, brightness / 2]));
                img.put_pixel(x, y + 1, Rgb([brightness / 2, brightness / 2, brightness / 2]));
            }
        }
    }
    
    img.save("assets/space_background.png").unwrap();
    println!("📸 太空背景已生成");
}

fn generate_spaceship() {
    let width = 64;
    let height = 64;
    let mut img: RgbImage = ImageBuffer::new(width, height);
    
    // 透明背景
    for pixel in img.pixels_mut() {
        *pixel = Rgb([0, 0, 0]);
    }
    
    let center_x = width / 2;
    let center_y = height / 2;
    
    // 绘制飞船主体（银色三角形）
    for y in 10..50 {
        let wing_width = ((y - 10) as f32 / 40.0 * 20.0) as u32;
        for x in (center_x - wing_width / 2)..(center_x + wing_width / 2) {
            if x < width && y < height {
                img.put_pixel(x, y, Rgb([180, 180, 200])); // 银色
            }
        }
    }
    
    // 飞船顶部（更亮的银色）
    for y in 10..20 {
        let tip_width = ((y - 10) as f32 / 10.0 * 8.0) as u32;
        for x in (center_x - tip_width / 2)..(center_x + tip_width / 2) {
            if x < width && y < height {
                img.put_pixel(x, y, Rgb([220, 220, 240])); // 亮银色
            }
        }
    }
    
    // 引擎喷口（蓝色）
    for y in 45..50 {
        for x in (center_x - 3)..(center_x + 3) {
            if x < width && y < height {
                img.put_pixel(x, y, Rgb([50, 150, 255])); // 蓝色引擎
            }
        }
    }
    
    img.save("assets/player.png").unwrap();
    println!("🚀 太空飞船已生成");
}

fn generate_asteroid() {
    let width = 48;
    let height = 48;
    let mut img: RgbImage = ImageBuffer::new(width, height);
    
    // 透明背景
    for pixel in img.pixels_mut() {
        *pixel = Rgb([0, 0, 0]);
    }
    
    let center_x = width as f32 / 2.0;
    let center_y = height as f32 / 2.0;
    let radius = 20.0;
    
    use rand::prelude::*;
    let mut rng = thread_rng();
    
    // 绘制不规则的陨石
    for y in 0..height {
        for x in 0..width {
            let dx = x as f32 - center_x;
            let dy = y as f32 - center_y;
            let distance = (dx * dx + dy * dy).sqrt();
            
            // 添加一些随机性使陨石看起来不规则
            let noise = rng.gen_range(-3.0..3.0);
            let adjusted_radius = radius + noise;
            
            if distance < adjusted_radius {
                // 陨石颜色（棕灰色系）
                let base_color = 80 + (distance / radius * 40.0) as u8;
                let r = base_color + rng.gen_range(0..20);
                let g = base_color - rng.gen_range(0..15);
                let b = base_color - rng.gen_range(0..25);
                
                img.put_pixel(x, y, Rgb([r, g, b]));
            }
        }
    }
    
    img.save("assets/enemy.png").unwrap();
    println!("☄️ 陨石已生成");
}

fn generate_laser() {
    let width = 8;
    let height = 16;
    let mut img: RgbImage = ImageBuffer::new(width, height);
    
    // 透明背景
    for pixel in img.pixels_mut() {
        *pixel = Rgb([0, 0, 0]);
    }
    
    // 绘制激光束（亮绿色）
    for y in 0..height {
        for x in 2..6 {
            let intensity = 255 - (((x as f32 - 4.0).abs() / 2.0) * 100.0) as u8;
            img.put_pixel(x, y, Rgb([0, intensity, 0])); // 绿色激光
        }
    }
    
    // 激光核心（更亮）
    for y in 0..height {
        img.put_pixel(3, y, Rgb([150, 255, 150]));
        img.put_pixel(4, y, Rgb([150, 255, 150]));
    }
    
    img.save("assets/bullet.png").unwrap();
    println!("⚡ 激光子弹已生成");
}

fn main() {
    generate_all_assets();
}
