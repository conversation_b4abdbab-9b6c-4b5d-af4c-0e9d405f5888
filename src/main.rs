use bevy::prelude::*;
use bevy::window::WindowResolution;
use rand::prelude::*;

// 游戏常量
const WINDOW_WIDTH: f32 = 800.0;
const WINDOW_HEIGHT: f32 = 600.0;
const PLAYER_SPEED: f32 = 300.0;
const BULLET_SPEED: f32 = 500.0;
const ENEMY_SPEED: f32 = 150.0;
const ENEMY_SPAWN_TIME: f32 = 1.0;

// 组件定义
#[derive(Component)]
struct Player;

#[derive(Component)]
struct Enemy;

#[derive(Component)]
struct Bullet;

#[derive(Component)]
struct Velocity(Vec2);

#[derive(Component)]
struct Health(i32);

#[derive(Component)]
struct Score(i32);

// 资源定义
#[derive(Resource)]
struct EnemySpawnTimer(Timer);

#[derive(Resource, Clone)]
struct GameTextures {
    player: Handle<Image>,
    enemy: Handle<Image>,
    bullet: Handle<Image>,
    background: Handle<Image>,
}

// 游戏状态
#[derive(<PERSON>, Debug, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>q, <PERSON>h, Default)]
enum GameState {
    #[default]
    Playing,
    GameOver,
}

fn main() {
    App::new()
        .add_plugins(DefaultPlugins.set(WindowPlugin {
            primary_window: Some(Window {
                resolution: WindowResolution::new(WINDOW_WIDTH, WINDOW_HEIGHT),
                title: "太空射击游戏".to_string(),
                resizable: false,
                ..default()
            }),
            ..default()
        }))
        .add_state::<GameState>()
        .insert_resource(EnemySpawnTimer(Timer::from_seconds(ENEMY_SPAWN_TIME, TimerMode::Repeating)))
        .add_systems(Startup, setup)
        .add_systems(Update, (
            player_movement,
            player_shooting,
            bullet_movement,
            enemy_movement,
            spawn_enemies,
            collision_system,
            cleanup_system,
        ).run_if(in_state(GameState::Playing)))
        .add_systems(Update, (
            game_over_input,
        ).run_if(in_state(GameState::GameOver)))
        .run();
}

// 初始化系统
fn setup(
    mut commands: Commands,
    asset_server: Res<AssetServer>,
) {
    // 创建相机
    commands.spawn(Camera2dBundle::default());

    // 初始化游戏
    setup_game(commands, asset_server);
}

// 玩家移动系统
fn player_movement(
    keyboard_input: Res<Input<KeyCode>>,
    mut player_query: Query<&mut Transform, With<Player>>,
    time: Res<Time>,
) {
    if let Ok(mut transform) = player_query.get_single_mut() {
        let mut direction = Vec2::ZERO;

        if keyboard_input.pressed(KeyCode::A) || keyboard_input.pressed(KeyCode::Left) {
            direction.x -= 1.0;
        }
        if keyboard_input.pressed(KeyCode::D) || keyboard_input.pressed(KeyCode::Right) {
            direction.x += 1.0;
        }
        if keyboard_input.pressed(KeyCode::W) || keyboard_input.pressed(KeyCode::Up) {
            direction.y += 1.0;
        }
        if keyboard_input.pressed(KeyCode::S) || keyboard_input.pressed(KeyCode::Down) {
            direction.y -= 1.0;
        }

        if direction != Vec2::ZERO {
            direction = direction.normalize();
            transform.translation += (direction * PLAYER_SPEED * time.delta_seconds()).extend(0.0);
        }

        // 限制玩家在屏幕范围内
        transform.translation.x = transform.translation.x.clamp(-WINDOW_WIDTH / 2.0 + 15.0, WINDOW_WIDTH / 2.0 - 15.0);
        transform.translation.y = transform.translation.y.clamp(-WINDOW_HEIGHT / 2.0 + 15.0, WINDOW_HEIGHT / 2.0 - 15.0);
    }
}

// 玩家射击系统
fn player_shooting(
    mut commands: Commands,
    keyboard_input: Res<Input<KeyCode>>,
    player_query: Query<&Transform, With<Player>>,
    game_textures: Res<GameTextures>,
) {
    if keyboard_input.just_pressed(KeyCode::Space) {
        if let Ok(player_transform) = player_query.get_single() {
            // 创建子弹（使用真实的激光图像）
            commands.spawn((
                SpriteBundle {
                    texture: game_textures.bullet.clone(),
                    transform: Transform::from_xyz(
                        player_transform.translation.x,
                        player_transform.translation.y + 30.0,
                        0.0,
                    ),
                    sprite: Sprite {
                        custom_size: Some(Vec2::new(12.0, 24.0)), // 增大激光尺寸
                        ..default()
                    },
                    ..default()
                },
                Bullet,
                Velocity(Vec2::new(0.0, BULLET_SPEED)),
            ));
        }
    }
}

// 子弹移动系统
fn bullet_movement(
    mut bullet_query: Query<(&mut Transform, &Velocity), With<Bullet>>,
    time: Res<Time>,
) {
    for (mut transform, velocity) in bullet_query.iter_mut() {
        transform.translation += (velocity.0 * time.delta_seconds()).extend(0.0);
    }
}

// 敌人移动系统
fn enemy_movement(
    mut enemy_query: Query<&mut Transform, With<Enemy>>,
    time: Res<Time>,
) {
    for mut transform in enemy_query.iter_mut() {
        transform.translation.y -= ENEMY_SPEED * time.delta_seconds();
    }
}

// 敌人生成系统
fn spawn_enemies(
    mut commands: Commands,
    mut timer: ResMut<EnemySpawnTimer>,
    time: Res<Time>,
    game_textures: Res<GameTextures>,
) {
    if timer.0.tick(time.delta()).just_finished() {
        let mut rng = thread_rng();
        let x = rng.gen_range(-WINDOW_WIDTH / 2.0 + 30.0..WINDOW_WIDTH / 2.0 - 30.0);

        commands.spawn((
            SpriteBundle {
                texture: game_textures.enemy.clone(),
                transform: Transform::from_xyz(x, WINDOW_HEIGHT / 2.0 + 30.0, 0.0),
                sprite: Sprite {
                    custom_size: Some(Vec2::new(56.0, 56.0)), // 增大陨石尺寸
                    ..default()
                },
                ..default()
            },
            Enemy,
            Health(1),
            Velocity(Vec2::new(0.0, -ENEMY_SPEED)),
        ));
    }
}

// 碰撞检测系统
fn collision_system(
    mut commands: Commands,
    bullet_query: Query<(Entity, &Transform), With<Bullet>>,
    enemy_query: Query<(Entity, &Transform), With<Enemy>>,
    player_query: Query<(Entity, &Transform), With<Player>>,
    mut score_query: Query<&mut Text, With<Score>>,
    mut score_resource: Query<&mut Score>,
    mut next_state: ResMut<NextState<GameState>>,
) {
    // 子弹与敌人碰撞
    for (bullet_entity, bullet_transform) in bullet_query.iter() {
        for (enemy_entity, enemy_transform) in enemy_query.iter() {
            let distance = bullet_transform.translation.distance(enemy_transform.translation);
            if distance < 20.0 {
                // 销毁子弹和敌人
                commands.entity(bullet_entity).despawn();
                commands.entity(enemy_entity).despawn();

                // 增加分数
                if let Ok(mut score) = score_resource.get_single_mut() {
                    score.0 += 10;
                    if let Ok(mut text) = score_query.get_single_mut() {
                        text.sections[0].value = format!("分数: {}", score.0);
                    }
                }
                break;
            }
        }
    }

    // 玩家与敌人碰撞
    if let Ok((_player_entity, player_transform)) = player_query.get_single() {
        for (enemy_entity, enemy_transform) in enemy_query.iter() {
            let distance = player_transform.translation.distance(enemy_transform.translation);
            if distance < 25.0 {
                // 游戏结束
                commands.entity(enemy_entity).despawn();
                next_state.set(GameState::GameOver);

                // 显示游戏结束界面（改进的样式）
                commands.spawn((
                    TextBundle::from_section(
                        "🚀 游戏结束 🚀",
                        TextStyle {
                            font_size: 48.0,
                            color: Color::rgb(1.0, 0.3, 0.3), // 亮红色
                            ..default()
                        },
                    )
                    .with_style(Style {
                        position_type: PositionType::Absolute,
                        top: Val::Px(200.0),
                        left: Val::Px(250.0),
                        ..default()
                    }),
                ));

                // 重新开始提示
                commands.spawn((
                    TextBundle::from_section(
                        "按 R 键重新开始",
                        TextStyle {
                            font_size: 28.0,
                            color: Color::rgb(0.8, 0.8, 1.0), // 淡蓝色
                            ..default()
                        },
                    )
                    .with_style(Style {
                        position_type: PositionType::Absolute,
                        top: Val::Px(280.0),
                        left: Val::Px(280.0),
                        ..default()
                    }),
                ));
                break;
            }
        }
    }
}

// 清理系统 - 移除超出屏幕的实体
fn cleanup_system(
    mut commands: Commands,
    bullet_query: Query<(Entity, &Transform), With<Bullet>>,
    enemy_query: Query<(Entity, &Transform), With<Enemy>>,
) {
    // 清理超出屏幕的子弹
    for (entity, transform) in bullet_query.iter() {
        if transform.translation.y > WINDOW_HEIGHT / 2.0 + 50.0 {
            commands.entity(entity).despawn();
        }
    }

    // 清理超出屏幕的敌人
    for (entity, transform) in enemy_query.iter() {
        if transform.translation.y < -WINDOW_HEIGHT / 2.0 - 50.0 {
            commands.entity(entity).despawn();
        }
    }
}

// 游戏结束输入处理
fn game_over_input(
    keyboard_input: Res<Input<KeyCode>>,
    mut next_state: ResMut<NextState<GameState>>,
    mut commands: Commands,
    entities: Query<Entity, (Without<Camera>, Without<Window>)>,
    asset_server: Res<AssetServer>,
) {
    if keyboard_input.just_pressed(KeyCode::R) {
        // 清理所有实体（除了相机和窗口）
        for entity in entities.iter() {
            commands.entity(entity).despawn();
        }

        // 重新初始化游戏
        setup_game(commands, asset_server);
        next_state.set(GameState::Playing);
    }
}

// 分离的游戏初始化函数
fn setup_game(
    mut commands: Commands,
    asset_server: Res<AssetServer>,
) {
    // 加载纹理
    let game_textures = GameTextures {
        player: asset_server.load("player.png"),
        enemy: asset_server.load("enemy.png"),
        bullet: asset_server.load("bullet.png"),
        background: asset_server.load("space_background.png"),
    };

    // 创建背景
    commands.spawn(SpriteBundle {
        texture: game_textures.background.clone(),
        transform: Transform::from_xyz(0.0, 0.0, -1.0), // 放在最后面
        ..default()
    });

    commands.insert_resource(game_textures.clone());

    // 创建玩家（使用真实的飞船图像）
    commands.spawn((
        SpriteBundle {
            texture: game_textures.player.clone(),
            transform: Transform::from_xyz(0.0, -250.0, 0.0),
            sprite: Sprite {
                custom_size: Some(Vec2::new(72.0, 72.0)), // J-20需要更大的显示尺寸
                ..default()
            },
            ..default()
        },
        Player,
        Health(3),
        Velocity(Vec2::ZERO),
    ));

    // 创建分数显示（改进的样式）
    commands.spawn((
        TextBundle::from_section(
            "分数: 0",
            TextStyle {
                font_size: 36.0,
                color: Color::rgb(0.9, 0.9, 1.0), // 淡蓝色
                ..default()
            },
        )
        .with_style(Style {
            position_type: PositionType::Absolute,
            top: Val::Px(20.0),
            left: Val::Px(20.0),
            ..default()
        }),
        Score(0),
    ));

    // 添加游戏说明
    commands.spawn(
        TextBundle::from_section(
            "WASD移动 | 空格射击",
            TextStyle {
                font_size: 20.0,
                color: Color::rgb(0.7, 0.7, 0.8),
                ..default()
            },
        )
        .with_style(Style {
            position_type: PositionType::Absolute,
            bottom: Val::Px(20.0),
            left: Val::Px(20.0),
            ..default()
        }),
    );
}
