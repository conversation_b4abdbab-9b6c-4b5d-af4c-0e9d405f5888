use std::env;
use std::fs;
use std::path::Path;

fn main() {
    // 获取目标目录
    let target_dir = if env::var("PROFILE").unwrap() == "release" {
        Path::new("target/release")
    } else {
        Path::new("target/debug")
    };
    
    // 如果是release构建，复制assets文件夹
    if env::var("PROFILE").unwrap() == "release" {
        let assets_src = Path::new("assets");
        let assets_dst = target_dir.join("assets");
        
        if assets_src.exists() {
            // 删除旧的assets文件夹（如果存在）
            if assets_dst.exists() {
                let _ = fs::remove_dir_all(&assets_dst);
            }
            
            // 复制assets文件夹
            copy_dir_all(&assets_src, &assets_dst).unwrap_or_else(|e| {
                println!("cargo:warning=Failed to copy assets: {}", e);
            });
            
            println!("cargo:warning=Assets copied to {:?}", assets_dst);
        }
    }
    
    // 告诉Cargo如果assets文件夹改变了就重新构建
    println!("cargo:rerun-if-changed=assets");
}

fn copy_dir_all(src: impl AsRef<Path>, dst: impl AsRef<Path>) -> std::io::Result<()> {
    fs::create_dir_all(&dst)?;
    for entry in fs::read_dir(src)? {
        let entry = entry?;
        let ty = entry.file_type()?;
        if ty.is_dir() {
            copy_dir_all(entry.path(), dst.as_ref().join(entry.file_name()))?;
        } else {
            fs::copy(entry.path(), dst.as_ref().join(entry.file_name()))?;
        }
    }
    Ok(())
}
