[package]
name = "space_shooter"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "generate_assets"
path = "src/generate_assets.rs"

[[bin]]
name = "space_shooter"
path = "src/main.rs"

[dependencies]
# 只启用需要的Bevy功能，减小文件大小
bevy = { version = "0.12", default-features = false, features = [
    "bevy_winit",        # 窗口管理
    "bevy_render",       # 渲染系统
    "bevy_sprite",       # 2D精灵
    "bevy_text",         # 文本渲染
    "bevy_ui",           # UI系统
    "png",               # PNG图像支持
    "default_font",      # 默认字体支持
    "x11",               # Linux支持（可选）
] }
rand = "0.8"
image = "0.24"

# 优化编译配置
[profile.dev]
opt-level = 3

[profile.dev.package."*"]
opt-level = 3

# 发布版本优化 - 减小文件大小
[profile.release]
opt-level = "z"          # 优化文件大小
lto = true              # 链接时优化
codegen-units = 1       # 减少代码生成单元
panic = "abort"         # 减少panic处理代码
strip = true            # 移除调试符号
