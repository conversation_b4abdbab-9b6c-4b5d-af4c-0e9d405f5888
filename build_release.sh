#!/bin/bash

echo "🔨 构建太空射击游戏发布版本..."
echo "================================"

# 生成资源文件
echo "📸 生成游戏资源..."
cargo run --bin generate_assets

# 构建发布版本
echo "🚀 编译发布版本..."
cargo build --release

# 复制资源文件到发布目录
echo "📁 复制资源文件..."
cp -r assets target/release/

echo "✅ 构建完成！"
echo ""
echo "🎮 运行方式："
echo "1. 在项目根目录运行: cargo run --release"
echo "2. 直接运行二进制文件: ./target/release/space_shooter"
echo "3. 进入发布目录运行: cd target/release && ./space_shooter"
echo ""
echo "📦 发布文件位置: target/release/"
echo "   - space_shooter (可执行文件)"
echo "   - assets/ (游戏资源)"
