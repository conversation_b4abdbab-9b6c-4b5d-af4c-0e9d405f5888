#!/bin/bash

echo "🚀 启动太空射击游戏..."
echo "================================"
echo "🎮 游戏特色："
echo "- 精美的太空背景和星空效果"
echo "- 真实的太空飞船和陨石图像"
echo "- 绿色激光武器系统"
echo "- 中文界面和优雅的UI设计"
echo ""
echo "🕹️  游戏操作："
echo "- 移动: WASD 键或方向键"
echo "- 射击: 空格键发射激光"
echo "- 重启: 游戏结束后按 R 键"
echo "================================"
echo ""

# 检查 Rust 是否安装
if ! command -v cargo &> /dev/null; then
    echo "❌ 错误: 未找到 Cargo。请先安装 Rust。"
    echo "访问 https://rustup.rs/ 安装 Rust"
    exit 1
fi

# 运行游戏
echo "🎮 正在启动游戏..."
cargo run --release

echo ""
echo "🎯 游戏已结束。感谢游玩！"
