#!/bin/bash

echo "🚀 启动太空射击游戏..."
echo "================================"
echo "游戏操作说明："
echo "- 移动: WASD 键或方向键"
echo "- 射击: 空格键"
echo "- 游戏结束后按 R 键重新开始"
echo "================================"
echo ""

# 检查 Rust 是否安装
if ! command -v cargo &> /dev/null; then
    echo "❌ 错误: 未找到 Cargo。请先安装 Rust。"
    echo "访问 https://rustup.rs/ 安装 Rust"
    exit 1
fi

# 运行游戏
echo "🎮 正在启动游戏..."
cargo run --release

echo ""
echo "🎯 游戏已结束。感谢游玩！"
